# Justel Integration for ailex-be-ingest

Production-ready integration for scraping and processing Belgian legal documents from the Justel database.

## 🎯 Overview

This integration provides comprehensive support for:
- **Consolidated legislation** from Belgium's official Justel database
- **French (FR) and Dutch (NL)** language support
- **Production-grade web scraping** using Playwright
- **LawDoc format** output for RAG/graph processing
- **Rate limiting and robots.txt compliance**
- **Comprehensive testing** with golden fixtures

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Docker (optional, for containerized deployment)
- Make (optional, for convenience commands)

### Installation

```bash
# Clone the repository
git clone https://github.com/Jpkay/ailex-be-ingest.git
cd ailex-be-ingest

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium

# Generate test fixtures
bash scripts/fetch_fixtures.sh

# Run tests to verify installation
pytest tests/test_golden_2022A30600.py -v
```

### Basic Usage

```bash
# Run French document ingestion (sample)
python -m sources.justel.cli run --language fr --limit 10

# Run Dutch document ingestion (sample)
python -m sources.justel.cli run --language nl --limit 10

# Resume from checkpoint
python -m sources.justel.cli resume

# Validate processed documents
python -m sources.justel.cli validate --sample 20

# Generate report
python -m sources.justel.cli report --format json
```

## 📋 Features

### ✅ Production Ready
- **Playwright-based scraping** with stealth mode
- **Rate limiting** (0.5 RPS default) with jitter
- **Robots.txt compliance** with automatic checking
- **Retry logic** with exponential backoff
- **Checkpoint management** with SQLite
- **Comprehensive logging** in JSONL format

### ✅ Data Quality
- **LawDoc schema compliance** per specification
- **Golden test fixtures** with real Justel HTML
- **Hash stability** for content and DOM structure
- **Article count validation** with ±25% tolerance
- **Schema validation** for all outputs

### ✅ Developer Experience
- **CLI interface** with rich progress output
- **Docker support** for consistent deployment
- **Makefile** with common development tasks
- **CI/CD pipeline** with automated testing
- **Comprehensive documentation**

## 🏗️ Architecture

```
sources/justel/
├── __init__.py          # Package initialization
├── client.py            # Playwright-based HTTP client
├── parse.py             # HTML parsing with language support
├── normalize.py         # LawDoc format conversion
├── ingest.py            # Complete ingestion pipeline
├── models.py            # Pydantic data models
├── selectors_fr.py      # French language patterns
├── selectors_nl.py      # Dutch language patterns
└── cli.py               # Typer CLI interface
```

## 🔧 Configuration

Environment variables (see `.env.example`):

```bash
# Justel Configuration
JUSTEL_RESPECT_ROBOTS=true
JUSTEL_RATE_LIMIT_RPS=0.5
JUSTEL_MAX_RETRIES=3
JUSTEL_CONCURRENCY=4
USER_AGENT="ailex-be-ingest/1.0"

# Output Directories
OUT_DIR_RAW="data/raw/justel"
OUT_DIR_PROCESSED="data/processed/justel"
CHECKPOINT_DB="data/checkpoints/justel.sqlite"
```

## 📊 Data Format

### LawDoc Schema (Production Format)

```json
{
  "schema_version": "1.0",
  "source": {
    "jurisdiction": "BE",
    "publisher": "Justel",
    "uri": "/eli/loi/2022/01/19/2022A30600/justel",
    "language": "fr",
    "snapshot_at": "2025-01-28T12:00:00Z"
  },
  "work": {
    "type": "law",
    "title": "Loi du 19 janvier 2022...",
    "number": "2022A30600",
    "status": "in_force",
    "date_enacted": "2022-01-19",
    "date_published": "2022-01-19",
    "date_in_force": "2022-01-19"
  },
  "eli": {
    "eli_uri": "/eli/loi/2022/01/19/2022A30600/justel",
    "canonical_uri": "/eli/loi/2022/01/19/2022A30600/justel",
    "other_ids": {"numac": "2022A30600"}
  },
  "relationships": {
    "amends": [],
    "amended_by": [],
    "repeals": [],
    "repealed_by": []
  },
  "structure": [
    {
      "level": "ARTICLE",
      "label": "Art. 1",
      "title": "Objet et champ d'application",
      "content": "La présente loi a pour objet...",
      "id": "art1"
    }
  ],
  "annexes": [],
  "hashes": {
    "content_hash": "sha256_hash_of_normalized_content",
    "dom_hash": "sha256_hash_of_dom_structure"
  },
  "fetched_at": "2025-01-28T12:00:00Z"
}
```

## 🧪 Testing

### Golden Tests

The integration includes golden tests with real Justel HTML:

```bash
# Run golden tests
make test-golden

# Or directly with pytest
pytest tests/test_golden_2022A30600.py -v
```

### Integration Tests

```bash
# Run all Justel tests
make test

# Run specific test suites
pytest tests/test_justel_client.py -v
pytest tests/test_justel_parser.py -v
pytest tests/test_justel_pipeline.py -v
```

### Validation

```bash
# Validate sample documents
python -m sources.justel.cli validate --sample 20

# Check schema compliance
python -m sources.justel.cli validate --schema-only
```

## 🐳 Docker Deployment

### Build Image

```bash
make docker-build
```

### Run Container

```bash
docker run --rm -it \
  -v $(PWD)/data:/app/data \
  -v $(PWD)/logs:/app/logs \
  --env-file .env \
  ailex-be-ingest:latest \
  python -m sources.justel.cli run --limit 100
```

## 📈 Monitoring

### Logs

Structured logs are written to `logs/` in JSONL format:

```json
{"timestamp": "2025-01-28T12:00:00Z", "level": "INFO", "message": "Starting ingestion", "numac": "2022A30600"}
```

### Metrics

Key metrics tracked:
- Documents processed per hour
- Success/failure rates
- Average processing time
- Rate limit compliance
- Schema validation results

### Health Checks

```bash
# Check system status
python -m sources.justel.cli status

# Validate recent outputs
python -m sources.justel.cli validate --recent
```

## 🔍 Troubleshooting

### Common Issues

1. **Rate Limiting**: Adjust `JUSTEL_RATE_LIMIT_RPS` if getting 429 errors
2. **Encoding Issues**: HTML files use ISO-8859-1 encoding
3. **Missing Articles**: Some ELI URLs point to metadata pages, not full text
4. **Browser Issues**: Ensure Playwright browsers are installed

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python -m sources.justel.cli run --dry-run --limit 1
```

## 📚 API Reference

### CLI Commands

- `run`: Start document ingestion
- `resume`: Resume from checkpoint
- `validate`: Validate processed documents
- `report`: Generate ingestion report
- `status`: Show system status

### Programmatic Usage

```python
from sources.justel import JustelClient, JustelNormalizer

# Initialize client
async with JustelClient() as client:
    # Fetch document
    html = await client.get_document_by_numac("2022A30600", "fr")
    
    # Normalize to LawDoc format
    normalizer = JustelNormalizer("fr")
    lawdoc = normalizer.normalize_to_lawdoc(
        html, "2022A30600", "/eli/loi/2022/01/19/2022A30600/justel"
    )
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the test files for usage examples
