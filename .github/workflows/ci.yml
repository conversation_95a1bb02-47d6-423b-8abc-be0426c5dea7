name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: "3.11"

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      neo4j:
        image: neo4j:5.0
        env:
          NEO4J_AUTH: neo4j/test_password
        ports:
          - 7687:7687
          - 7474:7474
        options: >-
          --health-cmd "cypher-shell -u neo4j -p test_password 'RETURN 1'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Install Playwright browsers
      run: |
        playwright install chromium
        playwright install-deps chromium

    - name: Set up test environment
      run: |
        mkdir -p data/checkpoints data/raw data/processed logs
        echo "TEST_MODE=true" >> $GITHUB_ENV
        echo "NEO4J_URI=bolt://localhost:7687" >> $GITHUB_ENV
        echo "NEO4J_USERNAME=neo4j" >> $GITHUB_ENV
        echo "NEO4J_PASSWORD=test_password" >> $GITHUB_ENV
        echo "VOYAGE_API_KEY=test_key" >> $GITHUB_ENV
        echo "PINECONE_API_KEY=test_key" >> $GITHUB_ENV
        echo "PINECONE_INDEX=test_index" >> $GITHUB_ENV

    - name: Wait for Neo4j
      run: |
        timeout 60 bash -c 'until nc -z localhost 7687; do sleep 1; done'

    - name: Run unit tests
      run: |
        pytest tests/test_vlaamse_codex.py -v --tb=short

    - name: Run integration tests
      run: |
        pytest tests/test_e2e_smoke.py -v -m "integration" --tb=short

    - name: Run checkpoint tests
      run: |
        pytest tests/test_checkpoint.py -v --tb=short

    - name: Run registry tests
      run: |
        pytest tests/test_registry.py -v --tb=short

    - name: Generate Justel test fixtures
      run: |
        mkdir -p tests/fixtures/html tests/fixtures/golden
        bash scripts/fetch_fixtures.sh

    - name: Run Justel unit tests
      run: |
        pytest tests/test_justel_client.py tests/test_justel_parser.py -v --tb=short

    - name: Run Justel golden tests
      run: |
        pytest tests/test_golden_2022A30600.py -v --tb=short

    - name: Run Justel integration tests
      run: |
        pytest tests/test_justel_pipeline.py -v --tb=short

    - name: Test Justel CLI commands
      run: |
        python -m sources.justel.cli --help
        python -m sources.justel.cli validate --sample 5 || echo "Validation skipped - no processed documents"

    - name: Check code formatting
      run: |
        pip install black isort
        black --check --diff .
        isort --check-only --diff .

    - name: Run linting
      run: |
        pip install flake8
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit

    - name: Check for security vulnerabilities
      run: |
        safety check --json || true
        bandit -r . -f json || true

  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Build package
      run: |
        python -m pip install build
        python -m build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  deploy-staging:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add actual deployment steps here

  resume-safe:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    services:
      neo4j:
        image: neo4j:5.0
        env:
          NEO4J_AUTH: neo4j/test_password
        ports:
          - 7687:7687
        options: >-
          --health-cmd "cypher-shell -u neo4j -p test_password 'RETURN 1'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Set up test environment
      run: |
        mkdir -p data/checkpoints data/raw data/processed logs
        echo "TEST_MODE=true" >> $GITHUB_ENV
        echo "NEO4J_URI=bolt://localhost:7687" >> $GITHUB_ENV
        echo "NEO4J_USERNAME=neo4j" >> $GITHUB_ENV
        echo "NEO4J_PASSWORD=test_password" >> $GITHUB_ENV
        echo "VOYAGE_API_KEY=test_key" >> $GITHUB_ENV
        echo "PINECONE_API_KEY=test_key" >> $GITHUB_ENV
        echo "PINECONE_INDEX=test_index" >> $GITHUB_ENV

    - name: Wait for Neo4j
      run: |
        timeout 60 bash -c 'until nc -z localhost 7687; do sleep 1; done'

    - name: Test resume-safe ingestion
      run: |
        # Start ingestion in background
        timeout 10s python pipelines/initial_load.py --dry-run --limit 20 --vlaamse-only &
        INGEST_PID=$!

        # Let it run for a few seconds
        sleep 5

        # Kill the process
        kill $INGEST_PID || true
        wait $INGEST_PID || true

        # Restart and verify it resumes
        python pipelines/initial_load.py --dry-run --limit 20 --vlaamse-only

        echo "✅ Resume-safe test completed"

  integrity:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Set up test environment
      run: |
        mkdir -p data/checkpoints data/raw data/processed logs
        echo "TEST_MODE=true" >> $GITHUB_ENV
        echo "SUPABASE_URL=https://test.supabase.co" >> $GITHUB_ENV
        echo "SUPABASE_KEY=test_key" >> $GITHUB_ENV
        echo "VOYAGE_API_KEY=test_key" >> $GITHUB_ENV
        echo "PINECONE_API_KEY=test_key" >> $GITHUB_ENV
        echo "PINECONE_INDEX=test_index" >> $GITHUB_ENV

    - name: Test audit tool
      run: |
        # Test audit with mocked data
        python tools/audit.py --source vlaamse --limit 10 --output test_audit.csv || true

        # Verify CSV was created
        if [ -f test_audit.csv ]; then
          echo "✅ Audit tool working"
        else
          echo "⚠️ Audit tool test skipped (no real data)"
        fi

    - name: Test repair tool
      run: |
        # Test repair in dry run mode
        python tools/repair.py --all --dry-run --limit 5 || true
        echo "✅ Repair tool test completed"

  deploy-production:
    runs-on: ubuntu-latest
    needs: [build, resume-safe]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add actual deployment steps here
