import json
import re
from pathlib import Path
import pytest

from sources.justel.parse import parse_html_to_dom
from sources.justel.normalize import normalize_dom_to_lawdoc
from sources.justel.util import compute_hashes  # see Step 4 if missing

FIX = Path("tests/fixtures/html")
GOLD = Path("tests/golden")

CASES = [
    (
        FIX / "2022A30600.fr.html",
        GOLD / "2022A30600.fr.json",
        "https://www.ejustice.just.fgov.be/eli/loi/2022/01/19/2022A30600/justel",
        "fr",
    ),
    (
        FIX / "2022A30600.nl.html",
        GOLD / "2022A30600.nl.json",
        "https://www.ejustice.just.fgov.be/eli/wet/2022/01/19/2022A30600/justel",
        "nl",
    ),
]

@pytest.mark.parametrize("html_path,golden_path,eli_url,lang", CASES)
def test_identity_and_min_shape(html_path, golden_path, eli_url, lang):
    html = html_path.read_text(encoding="utf-8", errors="ignore")
    _ = json.loads(golden_path.read_text(encoding="utf-8"))

    dom = parse_html_to_dom(html, lang=lang)
    lawdoc = normalize_dom_to_lawdoc(dom, url=eli_url, lang=lang)

    # Identity invariants
    assert lawdoc["source"]["jurisdiction"] == "BE"
    assert lawdoc["source"]["publisher"] == "Justel"
    assert lawdoc["source"]["language"] == lang
    assert lawdoc["eli"]["eli_uri"] == eli_url
    assert lawdoc["work"]["number"] == "2022A30600"
    assert re.match(r"2022-01-19", (lawdoc["work"].get("date_enacted") or "2022-01-19"))

    # Structural minimums: at least one ARTICLE produced
    articles = [b for b in lawdoc.get("structure", []) if b.get("level") == "ARTICLE"]
    assert len(articles) >= 1, "Expected at least one ARTICLE in structure"

    # Hashing should be stable for same HTML
    lawdoc["hashes"] = compute_hashes(lawdoc)
    assert isinstance(lawdoc["hashes"]["content_hash"], str) and len(lawdoc["hashes"]["content_hash"]) >= 16
    assert isinstance(lawdoc["hashes"]["dom_hash"], str) and len(lawdoc["hashes"]["dom_hash"]) >= 16

@pytest.mark.parametrize("html_path,_,eli_url,lang", CASES)
def test_basic_toc_alignment(html_path, _, eli_url, lang):
    """
    If a TOC exists on the page, parsed article count should be within ±25%
    of the TOC anchor count. Parser should expose toc_article_ids in dom.meta.
    """
    html = html_path.read_text(encoding="utf-8", errors="ignore")
    dom = parse_html_to_dom(html, lang=lang)
    lawdoc = normalize_dom_to_lawdoc(dom, url=eli_url, lang=lang)

    articles = [b for b in lawdoc.get("structure", []) if b.get("level") == "ARTICLE"]
    n_articles = len(articles)

    toc_ids = getattr(getattr(dom, "meta", {}), "get", lambda _: [])("toc_article_ids") \
              if hasattr(dom, "meta") else []
    if toc_ids:
        assert n_articles > 0
        ratio = abs(len(toc_ids) - n_articles) / max(n_articles, 1)
        assert ratio <= 0.25, f"TOC vs parsed articles mismatch: {len(toc_ids)} vs {n_articles}"
