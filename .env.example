# ailex-be-ingest Environment Configuration
# Copy this file to .env and configure for your environment

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# =============================================================================
# JUSTEL CONFIGURATION
# =============================================================================

# Base URLs for Justel ELI endpoints
JUSTEL_BASE_URL_FR=https://www.ejustice.just.fgov.be/eli/loi
JUSTEL_BASE_URL_NL=https://www.ejustice.just.fgov.be/eli/wet

# Rate limiting and behavior
JUSTEL_RESPECT_ROBOTS=true
JUSTEL_RATE_LIMIT_RPS=0.5
JUSTEL_MAX_RETRIES=3
JUSTEL_CONCURRENCY=4

# User agent for requests
USER_AGENT="ailex-be-ingest/1.0 (+https://github.com/Jpkay/ailex-be-ingest)"

# =============================================================================
# DATA STORAGE PATHS
# =============================================================================

# Raw data storage
OUT_DIR_RAW=data/raw/justel

# Processed data storage
OUT_DIR_PROCESSED=data/processed/justel

# Checkpoint database
CHECKPOINT_DB=data/checkpoints/justel.sqlite

# Log directory
LOG_DIR=logs

# Reports directory
REPORTS_DIR=reports

# =============================================================================
# PROCESSING CONFIGURATION
# =============================================================================

# Text processing
CHUNK_SIZE=1000
CHUNK_OVERLAP=100
BATCH_EMBED_SIZE=64

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================

# Voyage AI for embeddings
VOYAGE_API_KEY=your_voyage_api_key_here

# Pinecone configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=ailex-legal-docs

# =============================================================================
# GRAPH DATABASE CONFIGURATION
# =============================================================================

# Neo4j configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_neo4j_password_here

# =============================================================================
# GLOBAL REGISTRY CONFIGURATION
# =============================================================================

# Supabase configuration for global registry
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_anon_key_here

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development flags
DEBUG=false
DRY_RUN=false
SAVE_RAW_FILES=true

# Test configuration
TEST_DATA_DIR=tests/fixtures
GOLDEN_DATA_DIR=tests/fixtures/golden

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# Production flags
ENABLE_CACHING=true
CACHE_TTL=3600

# Performance tuning
MAX_WORKERS=4
BATCH_SIZE=10

# Monitoring
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=300

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Container configuration
CONTAINER_NAME=ailex-be-ingest
CONTAINER_PORT=8080

# Volume mounts
DATA_VOLUME=/app/data
LOGS_VOLUME=/app/logs
CONFIG_VOLUME=/app/config