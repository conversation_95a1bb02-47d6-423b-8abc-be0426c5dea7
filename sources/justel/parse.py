"""
Justel HTML parser for ailex-be-ingest.

Handles parsing of Belgian legal documents from Justel's HTML format,
extracting structured content and metadata.
"""

import re
import logging
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup, Tag, NavigableString

from .models import NumacDocument, EliDocument

logger = logging.getLogger(__name__)


class JustelParser:
    """Parser for Belgian legal documents from Justel."""
    
    def __init__(self, language: str = "fr"):
        """
        Initialize parser for specific language.
        
        Args:
            language: Language code (fr or nl)
        """
        self.language = language
        
        if language == "fr":
            from .selectors_fr import SELECTORS, PATTERNS, MARKERS, MONTH_NAMES, CLEANUP_PATTERNS
        else:
            from .selectors_nl import SELECTORS, PATTERNS, MARKERS, MONTH_NAMES, CLEANUP_PATTERNS
        
        self.selectors = SELECTORS
        self.patterns = PATTERNS
        self.markers = MARKERS
        self.month_names = MONTH_NAMES
        self.cleanup_patterns = CLEANUP_PATTERNS
    
    def parse_document(self, html: str) -> Optional[Dict[str, Any]]:
        """
        Parse a complete legal document from HTML.
        
        Args:
            html: HTML content of the document
            
        Returns:
            Parsed document data or None if parsing fails
        """
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Extract basic metadata
            metadata = self._extract_metadata(soup)
            if not metadata:
                logger.warning("Could not extract document metadata")
                return None
            
            # Extract document structure
            structure = self._extract_structure(soup)
            
            # Extract articles
            articles = self._extract_articles(soup)
            
            # Extract legal references
            references = self._extract_references(soup)
            
            return {
                "metadata": metadata,
                "structure": structure,
                "articles": articles,
                "references": references,
                "language": self.language,
                "parsed_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error parsing document: {e}")
            return None
    
    def _extract_metadata(self, soup: BeautifulSoup) -> Optional[Dict[str, Any]]:
        """Extract document metadata from HTML."""
        metadata = {}
        
        # Extract title
        title_elem = soup.select_one(self.selectors["title"])
        if title_elem:
            metadata["title"] = self._clean_text(title_elem.get_text())
        
        # Extract NUMAC
        numac = self._extract_numac(soup)
        if numac:
            metadata["numac"] = numac
        
        # Extract ELI
        eli = self._extract_eli(soup)
        if eli:
            metadata["eli"] = eli
        
        # Extract publication date
        pub_date = self._extract_publication_date(soup)
        if pub_date:
            metadata["publication_date"] = pub_date.isoformat()
        
        # Extract document type
        doc_type = self._extract_document_type(soup, metadata.get("title", ""))
        if doc_type:
            metadata["document_type"] = doc_type
        
        return metadata if metadata else None
    
    def _extract_numac(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract NUMAC identifier from document."""
        # Try selector first
        numac_elem = soup.select_one(self.selectors["numac"])
        if numac_elem:
            text = numac_elem.get_text()
            match = re.search(self.patterns["numac_reference"], text)
            if match:
                return match.group(1)
        
        # Try pattern search in full text
        text = soup.get_text()
        match = re.search(self.patterns["numac_reference"], text)
        if match:
            return match.group(1)
        
        return None
    
    def _extract_eli(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract ELI identifier from document."""
        # Try selector first
        eli_elem = soup.select_one(self.selectors["eli"])
        if eli_elem:
            text = eli_elem.get_text()
            match = re.search(self.patterns["eli_reference"], text)
            if match:
                return match.group(1)
        
        # Try pattern search in full text
        text = soup.get_text()
        match = re.search(self.patterns["eli_reference"], text)
        if match:
            return match.group(1)
        
        return None
    
    def _extract_publication_date(self, soup: BeautifulSoup) -> Optional[date]:
        """Extract publication date from document."""
        # Try selector first
        date_elem = soup.select_one(self.selectors["publication_date"])
        if date_elem:
            date_text = date_elem.get_text()
            parsed_date = self._parse_date(date_text)
            if parsed_date:
                return parsed_date
        
        # Try pattern search in full text
        text = soup.get_text()
        match = re.search(self.patterns["date_pattern"], text)
        if match:
            day, month_name, year = match.groups()
            month = self.month_names.get(month_name.lower())
            if month:
                try:
                    return date(int(year), month, int(day))
                except ValueError:
                    pass
        
        return None
    
    def _extract_document_type(self, soup: BeautifulSoup, title: str) -> Optional[str]:
        """Extract document type from title or content."""
        title_lower = title.lower()
        
        if self.language == "fr":
            if "loi" in title_lower:
                return "loi"
            elif "arrêté royal" in title_lower:
                return "arrete_royal"
            elif "arrêté ministériel" in title_lower:
                return "arrete_ministeriel"
            elif "décret" in title_lower:
                return "decret"
            elif "ordonnance" in title_lower:
                return "ordonnance"
        else:  # Dutch
            if "wet" in title_lower:
                return "wet"
            elif "koninklijk besluit" in title_lower:
                return "koninklijk_besluit"
            elif "ministerieel besluit" in title_lower:
                return "ministerieel_besluit"
            elif "decreet" in title_lower:
                return "decreet"
            elif "ordonnantie" in title_lower:
                return "ordonnantie"
        
        return None
    
    def _extract_structure(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract document structure (chapters, sections, etc.)."""
        structure = {
            "chapters": [],
            "sections": [],
            "toc": None
        }
        
        # Extract table of contents
        toc_elem = soup.select_one(self.selectors["toc_selector"])
        if toc_elem:
            structure["toc"] = self._clean_text(toc_elem.get_text())
        
        # Extract chapters
        text = soup.get_text()
        chapter_matches = re.finditer(self.patterns["heading_pattern"], text)
        for match in chapter_matches:
            chapter_num = match.group(1)
            chapter_title = match.group(2) if len(match.groups()) > 1 else ""
            structure["chapters"].append({
                "number": chapter_num,
                "title": chapter_title.strip() if chapter_title else ""
            })
        
        # Extract sections
        section_matches = re.finditer(self.patterns["section_pattern"], text)
        for match in section_matches:
            section_num = match.group(1)
            section_title = match.group(2) if len(match.groups()) > 1 else ""
            structure["sections"].append({
                "number": section_num,
                "title": section_title.strip() if section_title else ""
            })
        
        return structure
    
    def _extract_articles(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract articles from document."""
        articles = []
        
        # Try to find article containers first
        article_containers = soup.select(self.selectors["article_container"])
        
        if article_containers:
            for container in article_containers:
                article = self._parse_article_container(container)
                if article:
                    articles.append(article)
        else:
            # Fall back to pattern matching in text
            articles = self._extract_articles_by_pattern(soup)
        
        return articles
    
    def _parse_article_container(self, container: Tag) -> Optional[Dict[str, Any]]:
        """Parse an individual article container."""
        text = container.get_text()
        
        # Extract article number
        match = re.search(self.patterns["article_pattern"], text)
        if not match:
            return None
        
        article_num = match.group(1)
        
        # Clean and extract content
        content = self._clean_text(text)
        
        return {
            "number": article_num,
            "text": content,
            "html": str(container)
        }
    
    def _extract_articles_by_pattern(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract articles using regex patterns."""
        articles = []
        text = soup.get_text()
        
        # Find all article matches
        matches = list(re.finditer(self.patterns["article_pattern"], text))
        
        for i, match in enumerate(matches):
            article_num = match.group(1)
            start_pos = match.start()
            
            # Find end position (start of next article or end of text)
            if i + 1 < len(matches):
                end_pos = matches[i + 1].start()
            else:
                end_pos = len(text)
            
            # Extract article content
            article_text = text[start_pos:end_pos].strip()
            cleaned_text = self._clean_text(article_text)
            
            if cleaned_text:
                articles.append({
                    "number": article_num,
                    "text": cleaned_text,
                    "html": ""  # No HTML available in pattern mode
                })
        
        return articles
    
    def _extract_references(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract legal references from document."""
        references = []
        text = soup.get_text()
        
        # Extract NUMAC references
        numac_matches = re.finditer(self.patterns["numac_reference"], text)
        for match in numac_matches:
            references.append({
                "type": "numac",
                "identifier": match.group(1),
                "context": text[max(0, match.start()-50):match.end()+50]
            })
        
        # Extract ELI references
        eli_matches = re.finditer(self.patterns["eli_reference"], text)
        for match in eli_matches:
            references.append({
                "type": "eli",
                "identifier": match.group(1),
                "context": text[max(0, match.start()-50):match.end()+50]
            })
        
        return references
    
    def _parse_date(self, date_text: str) -> Optional[date]:
        """Parse date from various formats."""
        # Try full date pattern
        match = re.search(self.patterns["date_pattern"], date_text)
        if match:
            day, month_name, year = match.groups()
            month = self.month_names.get(month_name.lower())
            if month:
                try:
                    return date(int(year), month, int(day))
                except ValueError:
                    pass
        
        # Try short date pattern
        match = re.search(self.patterns["date_short"], date_text)
        if match:
            day, month, year = match.groups()
            try:
                return date(int(year), int(month), int(day))
            except ValueError:
                pass
        
        return None
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Apply cleanup patterns
        for pattern_name, pattern in self.cleanup_patterns.items():
            if pattern_name == "whitespace":
                text = re.sub(pattern, " ", text)
            elif pattern_name == "punctuation":
                text = re.sub(pattern, r"\1 \2", text)
            else:
                text = re.sub(pattern, "", text)
        
        return text


# --- Compatibility wrapper for tests ---
from typing import Any, Dict

def parse_html_to_dom(html: str, lang: str) -> Dict[str, Any]:
    """
    Thin wrapper to keep tests simple.
    Uses your existing JustelParser to return the parsed DOM dict.
    """
    try:
        parser = JustelParser(language=lang)  # your class
    except TypeError:
        # Fallback if your constructor signature differs
        parser = JustelParser(lang)
    dom = parser.parse_document(html)  # your method
    # Ensure a dict is returned (tests handle absence of dom.meta gracefully)
    if hasattr(dom, "dict"):
        return dom.dict()
    return dom  # assume it's already a dict-like.strip()
