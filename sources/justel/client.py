#!/usr/bin/env python3
# client.py
# Minimal, production-safe Justel fetcher (HTML only) with robots + rate limiting + retries.

from __future__ import annotations
import argparse
import asyncio
import time
import re
from pathlib import Path
from typing import Iterable, Optional
from urllib.parse import urlparse
import httpx
import sys
import random
import logging
import urllib.robotparser as robotparser

JUSTEL_HOST = "www.ejustice.just.fgov.be"
DEFAULT_UA = "ailex-be-ingest/1.0 (+https://example.com)"
ELI_RE = re.compile(
    r"/eli/(?P<type>loi|wet|arrete|besluit|constitution|grondwet)/"
    r"(?P<year>\d{4})/(?P<month>\d{2})/(?P<day>\d{2})/(?P<numac>[A-Za-z0-9]+)/justel$"
)

LANG_FROM_TYPE = {
    "loi": "fr",
    "arrete": "fr",
    "constitution": "fr",
    "wet": "nl",
    "besluit": "nl",
    "grondwet": "nl",
}


class RateLimiter:
    """Simple async rate limiter in requests-per-second."""
    def __init__(self, rps: float):
        self.rps = max(0.01, rps)
        self._lock = asyncio.Lock()
        self._next_time = 0.0

    async def wait(self):
        async with self._lock:
            now = time.monotonic()
            if now < self._next_time:
                await asyncio.sleep(self._next_time - now)
            # schedule next slot
            self._next_time = max(now, self._next_time) + (1.0 / self.rps)


class JustelClient:
    def __init__(
        self,
        outdir: Path,
        rps: float = 0.5,
        timeout: float = 45.0,
        user_agent: str = DEFAULT_UA,
        respect_robots: bool = True,
        max_retries: int = 3,
        jitter: tuple[float, float] = (0.2, 0.6),
    ):
        self.outdir = outdir
        self.outdir.mkdir(parents=True, exist_ok=True)
        self.timeout = timeout
        self.ua = user_agent
        self.respect_robots = respect_robots
        self.max_retries = max_retries
        self.jitter = jitter
        self.rate = RateLimiter(rps)
        self._robots: Optional[robotparser.RobotFileParser] = None

    async def _load_robots(self, client: httpx.AsyncClient):
        if not self.respect_robots:
            return
        if self._robots is not None:
            return
        robots_url = f"https://{JUSTEL_HOST}/robots.txt"
        try:
            resp = await client.get(robots_url, headers={"User-Agent": self.ua}, timeout=self.timeout)
            rp = robotparser.RobotFileParser()
            rp.parse(resp.text.splitlines())
            self._robots = rp
            logging.info("Loaded robots.txt from %s", robots_url)
        except Exception as e:
            logging.warning("Could not load robots.txt (%s). Proceeding cautiously.", e)
            self._robots = robotparser.RobotFileParser()
            self._robots.disallow_all = False  # fallback: allow

    def _can_fetch(self, url: str) -> bool:
        if not self.respect_robots or self._robots is None:
            return True
        return self._robots.can_fetch(self.ua, url)

    @staticmethod
    def parse_eli(url: str) -> dict:
        """
        Extract language, year, NUMAC-like id and type from an ELI URL.
        Raises ValueError if the URL doesn't match the ELI pattern.
        """
        parsed = urlparse(url)
        if parsed.netloc != JUSTEL_HOST:
            raise ValueError(f"Unexpected host: {parsed.netloc} (expected {JUSTEL_HOST})")
        m = ELI_RE.search(parsed.path)
        if not m:
            raise ValueError(f"Not an ELI consolidated URL: {url}")
        d = m.groupdict()
        lang = LANG_FROM_TYPE.get(d["type"])
        if not lang:
            raise ValueError(f"Could not infer language from type={d['type']} in {url}")
        return {
            "language": lang,
            "type": d["type"],
            "year": d["year"],
            "month": d["month"],
            "day": d["day"],
            "numac": d["numac"],
        }

    def dest_path_for(self, url: str) -> Path:
        meta = self.parse_eli(url)
        # data/raw/justel/{fr|nl}/{YYYY}/{NUMAC}.html
        dest = self.outdir / meta["language"] / meta["year"]
        dest.mkdir(parents=True, exist_ok=True)
        return dest / f"{meta['numac']}.html"

    async def fetch_one(self, client: httpx.AsyncClient, url: str) -> Path:
        """
        Fetch a single ELI URL and save HTML to the destination path.
        Respects robots, rate limit, and retries (429/5xx).
        """
        dest = self.dest_path_for(url)
        if dest.exists():  # idempotent
            logging.info("Skip existing: %s", dest)
            return dest

        if not self._can_fetch(url):
            raise PermissionError(f"Blocked by robots.txt: {url}")

        attempt = 0
        while True:
            attempt += 1
            await self.rate.wait()
            try:
                resp = await client.get(
                    url,
                    headers={"User-Agent": self.ua},
                    timeout=self.timeout,
                    follow_redirects=True,
                )
                if resp.status_code == 200 and "text/html" in resp.headers.get("content-type", ""):
                    dest.write_text(resp.text, encoding="utf-8")
                    logging.info("Saved: %s", dest)
                    return dest
                elif resp.status_code in (429, 500, 502, 503, 504):
                    raise httpx.HTTPStatusError(
                        f"Retryable status {resp.status_code}", request=resp.request, response=resp
                    )
                else:
                    # Save body for diagnostics
                    dest_err = dest.with_suffix(".err.html")
                    dest_err.write_text(resp.text, encoding="utf-8")
                    raise RuntimeError(f"Unexpected status {resp.status_code} for {url}; body saved to {dest_err}")
            except Exception as e:
                if attempt <= self.max_retries:
                    backoff = (2 ** (attempt - 1)) + random.uniform(*self.jitter)
                    logging.warning("Attempt %d failed for %s (%s). Retrying in %.2fs", attempt, url, e, backoff)
                    await asyncio.sleep(backoff)
                    continue
                raise

    async def fetch_many(self, urls: Iterable[str], concurrency: int = 3) -> list[Path]:
        """
        Fetch multiple URLs concurrently with bounded concurrency.
        """
        limits = httpx.Limits(max_keepalive_connections=concurrency, max_connections=concurrency)
        async with httpx.AsyncClient(limits=limits, http2=True) as client:
            await self._load_robots(client)
            sem = asyncio.Semaphore(concurrency)

            async def _task(u: str):
                async with sem:
                    return await self.fetch_one(client, u)

            tasks = [asyncio.create_task(_task(u)) for u in urls]
            results: list[Path] = []
            for t in asyncio.as_completed(tasks):
                try:
                    results.append(await t)
                except Exception as e:
                    logging.error("Failed: %s", e)
            return results


def parse_args(argv: list[str]) -> argparse.Namespace:
    p = argparse.ArgumentParser(
        description="Fetch consolidated Justel ELI HTML pages (FR/NL) with robots + rate limiting."
    )
    p.add_argument(
        "urls",
        nargs="+",
        help="One or more ELI URLs like "
             "https://www.ejustice.just.fgov.be/eli/loi/2022/01/19/2022A30600/justel "
             "or https://www.ejustice.just.fgov.be/eli/wet/2022/01/19/2022A30600/justel",
    )
    p.add_argument("--outdir", default="data/raw/justel", type=Path, help="Output root directory")
    p.add_argument("--rps", default=0.5, type=float, help="Requests per second (default: 0.5)")
    p.add_argument("--timeout", default=45.0, type=float, help="HTTP timeout seconds (default: 45)")
    p.add_argument("--respect-robots", action=argparse.BooleanOptionalAction, default=True)
    p.add_argument("--retries", default=3, type=int, help="Max retries for 429/5xx (default: 3)")
    p.add_argument("--concurrency", default=3, type=int, help="Concurrent fetches (default: 3)")
    p.add_argument("--user-agent", default=DEFAULT_UA, help="User-Agent header")
    p.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    return p.parse_args(argv)


async def amain(argv: list[str]) -> int:
    args = parse_args(argv)
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format="%(asctime)s %(levelname)s %(message)s",
    )
    client = JustelClient(
        outdir=args.outdir,
        rps=args.rps,
        timeout=args.timeout,
        user_agent=args.user_agent,
        respect_robots=args.respect_robots,
        max_retries=args.retries,
    )
    results = await client.fetch_many(args.urls, concurrency=args.concurrency)
    ok = sum(1 for _ in results)
    logging.info("Done. Saved %d files to %s", ok, args.outdir)
    return 0 if ok == len(args.urls) else 1


def main():
    try:
        raise SystemExit(asyncio.run(amain(sys.argv[1:])))
    except KeyboardInterrupt:
        print("\nInterrupted.", file=sys.stderr)
        raise SystemExit(130)


if __name__ == "__main__":
    main()
