import hashlib, json

def compute_hashes(doc: dict) -> dict:
    """
    content_hash: hash of normalized structure text
    dom_hash:     coarse hash using a few stable fields
    """
    structure_text = json.dumps(doc.get("structure", []), ensure_ascii=False, sort_keys=True)
    content_hash = hashlib.sha256(structure_text.encode("utf-8")).hexdigest()

    keybits = [
        doc.get("work", {}).get("number", ""),
        doc.get("source", {}).get("language", ""),
        str(len(doc.get("structure", []))),
    ]
    dom_hash = hashlib.sha256("|".join(keybits).encode("utf-8")).hexdigest()
    return {"content_hash": content_hash, "dom_hash": dom_hash}
