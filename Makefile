# Makefile for ailex-be-ingest Justel integration
.PHONY: help install fixtures test test-golden run-fr run-nl validate clean docker-build docker-run

# Default target
help:
	@echo "Available targets:"
	@echo "  install      - Install dependencies and set up environment"
	@echo "  fixtures     - Download test fixtures from Justel"
	@echo "  test         - Run all tests"
	@echo "  test-golden  - Run golden tests only"
	@echo "  run-fr       - Run French document ingestion (sample)"
	@echo "  run-nl       - Run Dutch document ingestion (sample)"
	@echo "  validate     - Validate processed documents"
	@echo "  clean        - Clean up generated files"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run in Docker container"

# Install dependencies
install:
	@echo "🔧 Installing dependencies..."
	pip install -r requirements.txt
	playwright install chromium
	@echo "✅ Installation complete"

# Download test fixtures
fixtures:
	@echo "📥 Downloading test fixtures..."
	bash scripts/fetch_fixtures.sh
	@echo "✅ Fixtures downloaded"

# Run all tests
test:
	@echo "🧪 Running all tests..."
	pytest tests/test_justel_*.py -v
	@echo "✅ Tests complete"

# Run golden tests only
test-golden:
	@echo "🏆 Running golden tests..."
	pytest tests/test_golden_2022A30600.py -v
	@echo "✅ Golden tests complete"

# Run French document ingestion (sample)
run-fr:
	@echo "🇫🇷 Running French document ingestion..."
	python -m sources.justel.cli run --language fr --limit 10 --dry-run
	@echo "✅ French ingestion complete"

# Run Dutch document ingestion (sample)
run-nl:
	@echo "🇳🇱 Running Dutch document ingestion..."
	python -m sources.justel.cli run --language nl --limit 10 --dry-run
	@echo "✅ Dutch ingestion complete"

# Validate processed documents
validate:
	@echo "✅ Validating processed documents..."
	python -m sources.justel.cli validate --sample 20
	@echo "✅ Validation complete"

# Clean up generated files
clean:
	@echo "🧹 Cleaning up..."
	rm -rf data/raw/justel/* data/processed/justel/* data/checkpoints/justel.sqlite
	rm -rf logs/* reports/*
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete
	@echo "✅ Cleanup complete"

# Build Docker image
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t ailex-be-ingest:latest .
	@echo "✅ Docker image built"

# Run in Docker container
docker-run:
	@echo "🐳 Running in Docker..."
	docker run --rm -it \
		-v $(PWD)/data:/app/data \
		-v $(PWD)/logs:/app/logs \
		-v $(PWD)/reports:/app/reports \
		--env-file .env \
		ailex-be-ingest:latest
	@echo "✅ Docker run complete"

# Development setup
dev-setup: install fixtures
	@echo "🚀 Development environment ready!"
	@echo "Run 'make test' to verify everything works"

# Production deployment check
prod-check: fixtures test validate
	@echo "🎯 Production readiness check complete!"
	@echo "System is ready for deployment"
